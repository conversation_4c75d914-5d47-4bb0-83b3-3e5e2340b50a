import AdminLayout from '@/Pages/Components/Layouts/AdminLayout'
import { Link, usePage } from '@inertiajs/react'
import ServerDataTable from '@/Pages/Components/ServerDataTable'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { columns } from '@/Pages/Admin/Users/<USER>'
import {
  Users as UsersIcon,
  UserPlus,
  Search,
  Filter,
  Download,
  MoreVertical,
  TrendingUp,
  UserCheck,
  UserX
} from 'lucide-react'

export default function Index({ users }) {
  const { flash } = usePage().props

  // Statistics based on pagination data
  const userStats = {
    total: users.total || 0,
    active: Math.floor((users.total || 0) * 0.8),
    inactive: Math.floor((users.total || 0) * 0.2),
    newThisMonth: Math.floor((users.total || 0) * 0.1)
  }

  return (
    <AdminLayout>
      <div className="space-y-8">
        {/* Header Section */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
              Users Management
            </h1>
            <p className="text-slate-600 mt-2">
              Manage and monitor all user accounts in your system
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm" className="gap-2">
              <Download className="h-4 w-4" />
              Export
            </Button>
            <Button variant="outline" size="sm" className="gap-2">
              <Filter className="h-4 w-4" />
              Filter
            </Button>
            <Link href="/users/create">
              <Button className="gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                <UserPlus className="h-4 w-4" />
                Add User
              </Button>
            </Link>
          </div>
        </div>

        {/* Flash Messages */}
        {flash.success && (
          <div className="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg flex items-center gap-2">
            <UserCheck className="h-5 w-5" />
            <span className="font-medium">{flash.success}</span>
          </div>
        )}

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">
                Total Users
              </CardTitle>
              <div className="p-2 rounded-lg bg-blue-100">
                <UsersIcon className="h-4 w-4 text-blue-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-slate-900">{userStats.total}</div>
              <p className="text-xs text-slate-500 mt-1">All registered users</p>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">
                Active Users
              </CardTitle>
              <div className="p-2 rounded-lg bg-green-100">
                <UserCheck className="h-4 w-4 text-green-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-slate-900">{userStats.active}</div>
              <p className="text-xs text-slate-500 mt-1">Currently active</p>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">
                Inactive Users
              </CardTitle>
              <div className="p-2 rounded-lg bg-orange-100">
                <UserX className="h-4 w-4 text-orange-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-slate-900">{userStats.inactive}</div>
              <p className="text-xs text-slate-500 mt-1">Need attention</p>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">
                New This Month
              </CardTitle>
              <div className="p-2 rounded-lg bg-purple-100">
                <TrendingUp className="h-4 w-4 text-purple-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-slate-900">{userStats.newThisMonth}</div>
              <p className="text-xs text-slate-500 mt-1">Recent registrations</p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="text-lg">Search & Filter Users</CardTitle>
            <CardDescription>
              Find specific users or filter by criteria
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <input
                  type="text"
                  placeholder="Search users by name or email..."
                  className="w-full pl-10 pr-4 py-2 border border-slate-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div className="flex gap-2">
                <select className="px-4 py-2 border border-slate-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <option>All Status</option>
                  <option>Active</option>
                  <option>Inactive</option>
                </select>
                <select className="px-4 py-2 border border-slate-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <option>All Roles</option>
                  <option>Admin</option>
                  <option>User</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Users Table */}
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg">Users List</CardTitle>
                <CardDescription>
                  Manage all user accounts and their permissions
                </CardDescription>
              </div>
              <Badge variant="outline" className="text-slate-600">
                {users.total || 0} users
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <ServerDataTable
              columns={columns}
              data={users.data || []}
              pagination={{
                current_page: users.current_page,
                last_page: users.last_page,
                per_page: users.per_page,
                total: users.total,
                from: users.from,
                to: users.to,
              }}
            />
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
