import AdminLayout from '@/Pages/Components/Layouts/AdminLayout'
import { Link, usePage } from '@inertiajs/react'
import DataTable from '@/Pages/Components/DataTable'
import { Button } from '@/components/ui/button'
import { columns } from '@/Pages/Admin/Users/<USER>'

export default function Index({ users }) {
  const { flash } = usePage().props

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-semibold">Users</h1>
          <Link href="/users/create">
            <Button>Add User</Button>
          </Link>
        </div>

        {flash.success && (
          <div className="text-green-600 font-medium">{flash.success}</div>
        )}
        <DataTable columns={columns} data={users.data} />
      </div>
    </AdminLayout>
  )
}
