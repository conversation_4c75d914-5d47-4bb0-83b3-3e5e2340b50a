import AdminLayout from "../Components/Layouts/AdminLayout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Users,
  TrendingUp,
  DollarSign,
  Activity,
  ArrowUpRight,
  ArrowDownRight,
  Eye,
  UserPlus,
  ShoppingCart,
  Calendar
} from "lucide-react"

export default function Dashboard() {
  const stats = [
    {
      title: "Total Users",
      value: "2,543",
      change: "+12%",
      trend: "up",
      icon: Users,
      color: "blue"
    },
    {
      title: "Revenue",
      value: "$45,231",
      change: "+8.2%",
      trend: "up",
      icon: DollarSign,
      color: "green"
    },
    {
      title: "Active Sessions",
      value: "1,234",
      change: "-2.4%",
      trend: "down",
      icon: Activity,
      color: "orange"
    },
    {
      title: "Growth Rate",
      value: "23.5%",
      change: "+4.1%",
      trend: "up",
      icon: TrendingUp,
      color: "purple"
    }
  ]

  const recentActivities = [
    { id: 1, action: "New user registered", user: "<PERSON>", time: "2 minutes ago", type: "user" },
    { id: 2, action: "Order completed", user: "<PERSON>", time: "5 minutes ago", type: "order" },
    { id: 3, action: "Profile updated", user: "<PERSON>", time: "10 minutes ago", type: "profile" },
    { id: 4, action: "New subscription", user: "Sarah Wilson", time: "15 minutes ago", type: "subscription" },
  ]

  return (
    <AdminLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
              Dashboard Overview
            </h1>
            <p className="text-slate-600 mt-2">
              Welcome back! Here's what's happening with your platform today.
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Badge variant="outline" className="text-green-600 border-green-200 bg-green-50">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              System Online
            </Badge>
            <div className="text-sm text-slate-500">
              Last updated: {new Date().toLocaleTimeString()}
            </div>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <Card key={index} className="relative overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-slate-600">
                  {stat.title}
                </CardTitle>
                <div className={`p-2 rounded-lg bg-${stat.color}-100`}>
                  <stat.icon className={`h-4 w-4 text-${stat.color}-600`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-slate-900">{stat.value}</div>
                <div className="flex items-center text-xs mt-2">
                  {stat.trend === "up" ? (
                    <ArrowUpRight className="h-3 w-3 text-green-500 mr-1" />
                  ) : (
                    <ArrowDownRight className="h-3 w-3 text-red-500 mr-1" />
                  )}
                  <span className={stat.trend === "up" ? "text-green-600" : "text-red-600"}>
                    {stat.change}
                  </span>
                  <span className="text-slate-500 ml-1">from last month</span>
                </div>
              </CardContent>
              <div className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-${stat.color}-400 to-${stat.color}-600 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300`}></div>
            </Card>
          ))}
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Activity */}
          <Card className="lg:col-span-2 border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5 text-blue-600" />
                Recent Activity
              </CardTitle>
              <CardDescription>
                Latest actions and events in your system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivities.map((activity) => (
                  <div key={activity.id} className="flex items-center gap-4 p-3 rounded-lg hover:bg-slate-50 transition-colors">
                    <div className={`p-2 rounded-full ${
                      activity.type === 'user' ? 'bg-blue-100' :
                      activity.type === 'order' ? 'bg-green-100' :
                      activity.type === 'profile' ? 'bg-orange-100' :
                      'bg-purple-100'
                    }`}>
                      {activity.type === 'user' && <UserPlus className="h-4 w-4 text-blue-600" />}
                      {activity.type === 'order' && <ShoppingCart className="h-4 w-4 text-green-600" />}
                      {activity.type === 'profile' && <Eye className="h-4 w-4 text-orange-600" />}
                      {activity.type === 'subscription' && <Calendar className="h-4 w-4 text-purple-600" />}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-slate-900">{activity.action}</p>
                      <p className="text-xs text-slate-500">{activity.user}</p>
                    </div>
                    <div className="text-xs text-slate-400">{activity.time}</div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="text-lg">Quick Actions</CardTitle>
              <CardDescription>
                Common tasks and shortcuts
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <button className="w-full p-3 text-left rounded-lg border border-slate-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 group">
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-blue-100 group-hover:bg-blue-200 transition-colors">
                    <UserPlus className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium text-slate-900">Add New User</p>
                    <p className="text-xs text-slate-500">Create a new user account</p>
                  </div>
                </div>
              </button>

              <button className="w-full p-3 text-left rounded-lg border border-slate-200 hover:border-green-300 hover:bg-green-50 transition-all duration-200 group">
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-green-100 group-hover:bg-green-200 transition-colors">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <p className="font-medium text-slate-900">View Analytics</p>
                    <p className="text-xs text-slate-500">Check detailed reports</p>
                  </div>
                </div>
              </button>

              <button className="w-full p-3 text-left rounded-lg border border-slate-200 hover:border-purple-300 hover:bg-purple-50 transition-all duration-200 group">
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-purple-100 group-hover:bg-purple-200 transition-colors">
                    <Activity className="h-4 w-4 text-purple-600" />
                  </div>
                  <div>
                    <p className="font-medium text-slate-900">System Status</p>
                    <p className="text-xs text-slate-500">Monitor system health</p>
                  </div>
                </div>
              </button>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  )
}
