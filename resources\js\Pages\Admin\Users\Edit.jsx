import { useForm, <PERSON> } from '@inertiajs/react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

export default function Edit({ user }) {
  const { data, setData, put, processing, errors } = useForm({
    name: user.name,
    email: user.email,
  })

  const handleSubmit = (e) => {
    e.preventDefault()
    put(`/users/${user.id}`)
  }

  return (
    <div>
      <h1 className="text-xl font-semibold mb-4">Edit User</h1>

      <form onSubmit={handleSubmit} className="space-y-4 max-w-md">
        <div>
          <Label htmlFor="name">Name</Label>
          <Input id="name" value={data.name} onChange={e => setData('name', e.target.value)} />
          {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
        </div>

        <div>
          <Label htmlFor="email">Email</Label>
          <Input id="email" value={data.email} onChange={e => setData('email', e.target.value)} />
          {errors.email && <p className="text-sm text-red-500">{errors.email}</p>}
        </div>

        <div className="flex items-center gap-3">
          <Button disabled={processing}>Update</Button>
          <Link href="/users" className="text-sm text-gray-600 hover:underline">Cancel</Link>
        </div>
      </form>
    </div>
  )
}
