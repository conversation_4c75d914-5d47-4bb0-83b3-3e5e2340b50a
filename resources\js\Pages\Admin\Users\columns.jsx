import { Button } from '@/components/ui/button'
import { Link } from '@inertiajs/react'

export const columns = [
  {
    accessorKey: 'name',
    header: 'Name',
  },
  {
    accessorKey: 'email',
    header: 'Email',
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => (
      <div className="flex justify-end gap-2">
        <Link href={`/admin/users/${row.original.id}/edit`}>
          <Button variant="outline" size="sm">Edit</Button>
        </Link>
        <Link
          as="button"
          method="delete"
          href={`/admin/users/${row.original.id}`}
          className="text-rose-600 text-sm hover:underline"
        >
          Delete
        </Link>
      </div>
    ),
  },
]
