import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Link } from '@inertiajs/react'
import {
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Mail,
  Calendar,
  Shield
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"

export const columns = [
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <div className="flex items-center gap-2 font-medium text-slate-700">
        <Shield className="h-4 w-4" />
        Name
      </div>
    ),
    cell: ({ row }) => {
      const user = row.original
      return (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-medium text-sm">
            {user.name?.charAt(0)?.toUpperCase() || 'U'}
          </div>
          <div>
            <p className="font-medium text-slate-900">{user.name}</p>
            <p className="text-sm text-slate-500">ID: #{user.id}</p>
          </div>
        </div>
      )
    },
  },
  {
    accessorKey: 'email',
    header: ({ column }) => (
      <div className="flex items-center gap-2 font-medium text-slate-700">
        <Mail className="h-4 w-4" />
        Email
      </div>
    ),
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <span className="text-slate-900">{row.getValue('email')}</span>
        <Badge variant="outline" className="text-xs text-green-600 border-green-200">
          Verified
        </Badge>
      </div>
    ),
  },
  {
    accessorKey: 'role',
    header: 'Role',
    cell: ({ row }) => {
      const role = row.original.role || 'user'
      return (
        <Badge
          variant={role === 'admin' ? 'default' : 'secondary'}
          className={role === 'admin' ? 'bg-purple-100 text-purple-700 border-purple-200' : ''}
        >
          {role.charAt(0).toUpperCase() + role.slice(1)}
        </Badge>
      )
    },
  },
  {
    accessorKey: 'created_at',
    header: ({ column }) => (
      <div className="flex items-center gap-2 font-medium text-slate-700">
        <Calendar className="h-4 w-4" />
        Joined
      </div>
    ),
    cell: ({ row }) => {
      const date = new Date(row.original.created_at || Date.now())
      return (
        <div>
          <p className="text-slate-900">{date.toLocaleDateString()}</p>
          <p className="text-xs text-slate-500">{date.toLocaleTimeString()}</p>
        </div>
      )
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => (
      <Badge variant="outline" className="text-green-600 border-green-200 bg-green-50">
        <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
        Active
      </Badge>
    ),
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => {
      const user = row.original

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem asChild>
              <Link href={`/users/${user.id}`} className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                View Details
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href={`/users/${user.id}/edit`} className="flex items-center gap-2">
                <Edit className="h-4 w-4" />
                Edit User
              </Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link
                as="button"
                method="delete"
                href={`/users/${user.id}`}
                className="flex items-center gap-2 text-red-600 hover:text-red-700 w-full"
                onClick={(e) => {
                  if (!confirm('Are you sure you want to delete this user?')) {
                    e.preventDefault()
                  }
                }}
              >
                <Trash2 className="h-4 w-4" />
                Delete User
              </Link>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]
