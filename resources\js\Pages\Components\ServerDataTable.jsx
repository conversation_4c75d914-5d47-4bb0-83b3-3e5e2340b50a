import { useState } from "react"
import { router } from "@inertiajs/react"
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table"
import {
  Table,
  TableHeader,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { 
  ChevronLeft, 
  ChevronRight, 
  ChevronsLeft, 
  ChevronsRight,
  MoreHorizontal
} from "lucide-react"

export default function ServerDataTable({ columns, data, pagination }) {
  const [pageSize, setPageSize] = useState(pagination.per_page)

  const table = useReactTable({
    data: data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    pageCount: pagination.last_page,
  })

  const handlePageChange = (page) => {
    router.get(window.location.pathname, {
      page: page,
      per_page: pageSize,
    }, {
      preserveState: true,
      preserveScroll: true,
    })
  }

  const handlePageSizeChange = (newPageSize) => {
    setPageSize(newPageSize)
    router.get(window.location.pathname, {
      page: 1,
      per_page: newPageSize,
    }, {
      preserveState: true,
      preserveScroll: true,
    })
  }

  return (
    <div className="space-y-4">
      <div className="rounded-lg border border-slate-200 overflow-hidden">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(hg => (
              <TableRow key={hg.id} className="bg-slate-50 hover:bg-slate-50">
                {hg.headers.map(header => (
                  <TableHead key={header.id} className="font-medium text-slate-700">
                    {flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {data?.length ? (
              data.map((row, index) => (
                <TableRow 
                  key={row.id || index} 
                  className="hover:bg-slate-50 transition-colors"
                >
                  {table.getHeaderGroups()[0].headers.map((header) => (
                    <TableCell key={header.id} className="py-4">
                      {flexRender(
                        header.column.columnDef.cell, 
                        { 
                          row: { 
                            original: row, 
                            getValue: (key) => row[key] 
                          } 
                        }
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  <div className="flex flex-col items-center gap-2 text-slate-500">
                    <MoreHorizontal className="h-8 w-8" />
                    <p>No results found.</p>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      
      {/* Server-side Pagination */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <p className="text-sm text-slate-600">
            Showing{" "}
            <span className="font-medium">{pagination.from || 0}</span>{" "}
            to{" "}
            <span className="font-medium">{pagination.to || 0}</span>{" "}
            of{" "}
            <span className="font-medium">{pagination.total}</span>{" "}
            results
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(1)}
              disabled={pagination.current_page === 1}
              className="h-8 w-8 p-0"
            >
              <ChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.current_page - 1)}
              disabled={pagination.current_page === 1}
              className="h-8 w-8 p-0"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="flex items-center gap-1">
            <span className="text-sm text-slate-600">
              Page{" "}
              <span className="font-medium">{pagination.current_page}</span>{" "}
              of{" "}
              <span className="font-medium">{pagination.last_page}</span>
            </span>
          </div>
          
          <div className="flex items-center gap-1">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.current_page + 1)}
              disabled={pagination.current_page === pagination.last_page}
              className="h-8 w-8 p-0"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.last_page)}
              disabled={pagination.current_page === pagination.last_page}
              className="h-8 w-8 p-0"
            >
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="flex items-center gap-2 ml-4">
            <span className="text-sm text-slate-600">Rows per page:</span>
            <select
              value={pageSize}
              onChange={(e) => handlePageSizeChange(Number(e.target.value))}
              className="h-8 px-2 text-sm border border-slate-200 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {[5, 10, 20, 30, 40, 50].map((size) => (
                <option key={size} value={size}>
                  {size}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>
    </div>
  )
}
