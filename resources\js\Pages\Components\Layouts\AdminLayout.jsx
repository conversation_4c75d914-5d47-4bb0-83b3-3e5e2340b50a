import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Separator } from "@/components/ui/separator"

import { Link, usePage } from "@inertiajs/react"
import { LogOut, LayoutDashboard, Users, Settings } from "lucide-react"

export default function AdminLayout({ children }) {
  const { url } = usePage()
  const appName = usePage().props.appName

  return (
    <div className="min-h-screen flex bg-gray-50 text-gray-800">
      {/* Sidebar */}
      <aside className="w-64 bg-white border-r border-gray-200">
        <div className="px-6 py-5 text-xl font-semibold tracking-tight text-rose-600">
          { appName }
        </div>
        <nav className="flex flex-col gap-1 px-3 mt-2 text-sm">
          <SidebarLink icon={LayoutDashboard} label="Dashboard" href="/" active={url.startsWith("/")} />
          <SidebarLink icon={Users} label="Users" href="/users" active={url.startsWith("/users")} />
          <SidebarLink icon={Settings} label="Settings" href="/settings" active={url.startsWith("/settings")} />
        </nav>
      </aside>

      {/* Main content */}
      <div className="flex-1 flex flex-col">
        {/* Topbar */}
        <header className="h-16 px-6 flex items-center justify-between bg-white border-b border-gray-200">
          <div className="text-base font-medium">Dashboard</div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Avatar className="cursor-pointer hover:ring-2 hover:ring-rose-500 transition">
                <AvatarImage src="https://i.pravatar.cc/300" alt="Admin" />
                <AvatarFallback>AD</AvatarFallback>
              </Avatar>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuLabel className="font-medium text-gray-700">Admin</DropdownMenuLabel>
              <Separator />
              <DropdownMenuItem asChild>
                <Link
                  href="/logout"
                  method="post"
                  className="flex items-center gap-2 text-rose-500 hover:text-rose-600"
                >
                  <LogOut size={16} /> Logout
                </Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </header>

        {/* Page Content */}
        <main className="p-6 flex-1 overflow-y-auto">{children}</main>
      </div>
    </div>
  )
}

function SidebarLink({ icon: Icon, label, href, active }) {
  return (
    <Link
      href={href}
      className={`flex items-center gap-3 px-4 py-2 rounded-md transition-colors group
        ${active
          ? "bg-rose-50 text-rose-600 font-medium"
          : "text-gray-600 hover:bg-gray-100 hover:text-rose-600"}`}
    >
      <Icon size={18} className="text-inherit" />
      <span>{label}</span>
    </Link>
  )
}
