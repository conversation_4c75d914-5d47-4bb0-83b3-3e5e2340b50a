import * as React from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"

import { Link, usePage } from "@inertiajs/react"
import {
  LogOut,
  LayoutDashboard,
  Users,
  Settings,
  Bell,
  Search,
  Menu,
  ChevronDown,
  Home,
  BarChart3,
  FileText,
  Shield
} from "lucide-react"

export default function AdminLayout({ children }) {
  const { url } = usePage()
  const appName = usePage().props.appName

  return (
    <div className="min-h-screen flex bg-gradient-to-br from-slate-50 to-slate-100">
      {/* Sidebar */}
      <aside className="w-72 bg-white/80 backdrop-blur-sm border-r border-slate-200/60 shadow-xl">
        {/* Logo Section */}
        <div className="px-6 py-6 border-b border-slate-200/60">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center">
              <Home className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                {appName}
              </h1>
              <p className="text-xs text-slate-500">Admin Panel</p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex flex-col gap-2 px-4 py-6">
          <div className="text-xs font-semibold text-slate-400 uppercase tracking-wider mb-2 px-3">
            Main Menu
          </div>
          <SidebarLink
            icon={LayoutDashboard}
            label="Dashboard"
            href="/"
            active={url === "/"}
            badge="3"
          />
          <SidebarLink
            icon={Users}
            label="Users Management"
            href="/users"
            active={url.startsWith("/users")}
          />
          <SidebarLink
            icon={BarChart3}
            label="Analytics"
            href="/analytics"
            active={url.startsWith("/analytics")}
          />
          <SidebarLink
            icon={FileText}
            label="Reports"
            href="/reports"
            active={url.startsWith("/reports")}
          />

          <div className="text-xs font-semibold text-slate-400 uppercase tracking-wider mb-2 px-3 mt-6">
            System
          </div>
          <SidebarLink
            icon={Settings}
            label="Settings"
            href="/settings"
            active={url.startsWith("/settings")}
          />
          <SidebarLink
            icon={Shield}
            label="Security"
            href="/security"
            active={url.startsWith("/security")}
          />
        </nav>

        {/* User Info Card */}
        <div className="absolute bottom-4 left-4 right-4">
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 border border-blue-100">
            <div className="flex items-center gap-3">
              <Avatar className="w-10 h-10 ring-2 ring-blue-200">
                <AvatarImage src="https://i.pravatar.cc/300" alt="Admin" />
                <AvatarFallback className="bg-blue-600 text-white">AD</AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-slate-900 truncate">Admin User</p>
                <p className="text-xs text-slate-500 truncate"><EMAIL></p>
              </div>
              <ChevronDown className="w-4 h-4 text-slate-400" />
            </div>
          </div>
        </div>
      </aside>

      {/* Main content */}
      <div className="flex-1 flex flex-col">
        {/* Enhanced Header */}
        <header className="h-16 px-6 flex items-center justify-between bg-white/80 backdrop-blur-sm border-b border-slate-200/60 shadow-sm">
          <div className="flex items-center gap-4">
            <Menu className="w-5 h-5 text-slate-400 lg:hidden" />
            <div>
              <h2 className="text-lg font-semibold text-slate-900">Dashboard</h2>
              <p className="text-xs text-slate-500">Welcome back, Admin</p>
            </div>
          </div>

          <div className="flex items-center gap-4">
            {/* Search */}
            <div className="relative hidden md:block">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
              <input
                type="text"
                placeholder="Search..."
                className="pl-10 pr-4 py-2 w-64 text-sm bg-slate-50 border border-slate-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Notifications */}
            <div className="relative">
              <Bell className="w-5 h-5 text-slate-400 hover:text-slate-600 cursor-pointer transition-colors" />
              <Badge className="absolute -top-2 -right-2 w-5 h-5 p-0 flex items-center justify-center text-xs bg-red-500 text-white">
                3
              </Badge>
            </div>

            {/* User Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <div className="flex items-center gap-2 cursor-pointer hover:bg-slate-50 rounded-lg p-2 transition-colors">
                  <Avatar className="w-8 h-8">
                    <AvatarImage src="https://i.pravatar.cc/300" alt="Admin" />
                    <AvatarFallback className="bg-blue-600 text-white text-xs">AD</AvatarFallback>
                  </Avatar>
                  <ChevronDown className="w-4 h-4 text-slate-400" />
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel className="font-medium text-slate-700">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium">Admin User</p>
                    <p className="text-xs text-slate-500"><EMAIL></p>
                  </div>
                </DropdownMenuLabel>
                <Separator />
                <DropdownMenuItem asChild>
                  <Link href="/profile" className="flex items-center gap-2">
                    <Users size={16} /> Profile
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/settings" className="flex items-center gap-2">
                    <Settings size={16} /> Settings
                  </Link>
                </DropdownMenuItem>
                <Separator />
                <DropdownMenuItem asChild>
                  <Link
                    href="/logout"
                    method="post"
                    className="flex items-center gap-2 text-red-600 hover:text-red-700"
                  >
                    <LogOut size={16} /> Logout
                  </Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </header>

        {/* Page Content */}
        <main className="p-6 flex-1 overflow-y-auto bg-gradient-to-br from-slate-50/50 to-white">
          {children}
        </main>
      </div>
    </div>
  )
}

function SidebarLink({ icon: Icon, label, href, active, badge }) {
  return (
    <Link
      href={href}
      className={`flex items-center gap-3 px-3 py-3 rounded-xl transition-all duration-200 group relative
        ${active
          ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg shadow-blue-500/25 font-medium"
          : "text-slate-600 hover:bg-slate-50 hover:text-blue-600 hover:shadow-sm"}`}
    >
      <div className={`p-1.5 rounded-lg transition-colors ${
        active
          ? "bg-white/20"
          : "bg-slate-100 group-hover:bg-blue-50"
      }`}>
        <Icon size={16} className={`transition-colors ${
          active ? "text-white" : "text-slate-500 group-hover:text-blue-600"
        }`} />
      </div>
      <span className="font-medium text-sm">{label}</span>
      {badge && (
        <Badge
          variant={active ? "secondary" : "default"}
          className={`ml-auto text-xs ${
            active
              ? "bg-white/20 text-white border-white/30"
              : "bg-blue-100 text-blue-600 border-blue-200"
          }`}
        >
          {badge}
        </Badge>
      )}
      {active && (
        <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-white rounded-l-full" />
      )}
    </Link>
  )
}
